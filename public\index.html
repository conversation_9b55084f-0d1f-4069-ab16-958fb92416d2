<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Fixed Iframe Click Tracker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }

        #iframe-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }

        #my-iframe {
            width: 100%;
            height: 100%;
            border: 2px solid #3498db;
            border-radius: 5px;
        }

        #click-counter {
            font-size: 1.5em;
            font-weight: bold;
            color: #e74c3c;
        }

        #click-feedback {
            position: absolute;
            background: rgba(46, 204, 113, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            display: none;
            font-weight: bold;
            font-size: 14px;
            z-index: 1000;
            pointer-events: none;
        }

        .stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        #auto-click-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
        }

        #auto-click-btn:hover {
            background: #c0392b;
        }

        #auto-click-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }

        #block-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(231, 76, 60, 0.3);
            z-index: 2000;
            display: none;
            cursor: not-allowed;
        }

        .stats-table {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stats-table th,
        .stats-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .stats-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }

        .stats-table tr:hover {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h2>Fixed Iframe Interaction Tracker</h2>
    <div class="stats">
        <div>
            <p>Total Clicks: <span id="click-counter">0</span></p>
            <p><small>Click anywhere inside the Wikipedia iframe below - links will work with single clicks!</small></p>
        </div>
        <button id="auto-click-btn">Auto Click (300x)</button>
    </div>
    
    <table class="stats-table" id="stats-table" style="display: none;">
        <thead>
            <tr>
                <th>Metric</th>
                <th>Count</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Total Clicks</td>
                <td id="table-total-clicks">0</td>
                <td>Manual clicks + First auto-click</td>
            </tr>
            <tr>
                <td>Block Count</td>
                <td id="table-block-count">0</td>
                <td>Auto-clicks that were blocked (299 per session)</td>
            </tr>
            <tr>
                <td>Auto-Click Sessions</td>
                <td id="table-auto-sessions">0</td>
                <td>Times auto-click button was used</td>
            </tr>
        </tbody>
    </table>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="click-feedback"></div>
        <div id="block-overlay"></div>
    </div>

    <script>
        (function () {
            let clickCount = 0;
            let blockCount = 0;
            let autoSessions = 0;
            
            const counter = document.getElementById('click-counter');
            const feedback = document.getElementById('click-feedback');
            const iframe = document.getElementById('my-iframe');
            const container = document.getElementById('iframe-container');
            const autoClickBtn = document.getElementById('auto-click-btn');
            const blockOverlay = document.getElementById('block-overlay');
            const statsTable = document.getElementById('stats-table');
            
            let mouseOverIframe = false;
            let lastClickTime = 0;
            let isAutoClicking = false;
            let overlayActive = false;

            // Update stats table
            function updateStatsTable() {
                document.getElementById('table-total-clicks').textContent = clickCount;
                document.getElementById('table-block-count').textContent = blockCount;
                document.getElementById('table-auto-sessions').textContent = autoSessions;
                statsTable.style.display = 'table';
            }

            // Auto-click functionality
            autoClickBtn.addEventListener('click', async () => {
                if (isAutoClicking) return;
                
                isAutoClicking = true;
                autoClickBtn.disabled = true;
                autoClickBtn.textContent = 'Auto Clicking...';
                autoSessions++;
                
                try {
                    // Wait for iframe to load completely
                    await waitForIframeLoad();
                    
                    // Get the h1 element from iframe
                    const h1Element = iframe.contentDocument.querySelector('#firstHeading');
                    
                    if (!h1Element) {
                        alert('Could not find the target element in iframe. Please ensure the page is loaded.');
                        resetAutoClickButton();
                        return;
                    }
                    
                    console.log('🎯 Starting auto-click sequence on:', h1Element);
                    console.log('🎯 Target element text:', h1Element.textContent);
                    console.log('🎯 Element position:', h1Element.getBoundingClientRect());
                    
                    // Perform 300 clicks in 1 second
                    let clicksPerformed = 0;
                    let firstClickCounted = false;
                    const startTime = Date.now();
                    
                    // Use a faster approach with requestAnimationFrame for better performance
                    function performClicks() {
                        const now = Date.now();
                        const elapsed = now - startTime;
                        const targetClicks = Math.floor((elapsed / 1000) * 300); // 300 clicks per second
                        
                        // Perform multiple clicks per frame if needed to catch up
                        while (clicksPerformed < targetClicks && clicksPerformed < 300) {
                            clicksPerformed++;
                            
                            // Create multiple event types for better compatibility
                            const mousedownEvent = new MouseEvent('mousedown', {
                                bubbles: true,
                                cancelable: true,
                                view: iframe.contentWindow,
                                button: 0,
                                clientX: 100,
                                clientY: 100
                            });
                            
                            const mouseupEvent = new MouseEvent('mouseup', {
                                bubbles: true,
                                cancelable: true,
                                view: iframe.contentWindow,
                                button: 0,
                                clientX: 100,
                                clientY: 100
                            });
                            
                            const clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: iframe.contentWindow,
                                button: 0,
                                clientX: 100,
                                clientY: 100
                            });
                            
                            // Dispatch all events for maximum compatibility
                            h1Element.dispatchEvent(mousedownEvent);
                            h1Element.dispatchEvent(mouseupEvent);
                            h1Element.dispatchEvent(clickEvent);
                            
                            // Also try focus and blur
                            if (clicksPerformed % 50 === 0) { // Every 50th click
                                h1Element.focus();
                                setTimeout(() => h1Element.blur(), 1);
                            }
                            
                            console.log(`🖱️ Auto-click ${clicksPerformed}/300 performed on:`, h1Element.tagName);
                            
                            // Only count the first click in total clicks
                            if (!firstClickCounted) {
                                clickCount++;
                                counter.textContent = clickCount;
                                
                                // Show feedback for first click
                                const rect = iframe.getBoundingClientRect();
                                showFeedback(
                                    rect.left + rect.width / 2,
                                    rect.top + 30,
                                    clickCount,
                                    'Auto-Click #1'
                                );
                                
                                firstClickCounted = true;
                                console.log(`✅ First auto-click (#${clicksPerformed}) counted in total: ${clickCount}`);
                            } else {
                                // Add to block count
                                blockCount++;
                                if (clicksPerformed % 25 === 0) { // Log every 25th blocked click
                                    console.log(`🚫 Blocked auto-click #${clicksPerformed}, total blocked: ${blockCount}`);
                                }
                            }
                        }
                        
                        // Continue if we haven't reached 300 clicks and haven't exceeded 1.5 seconds
                        if (clicksPerformed < 300 && elapsed < 1500) {
                            requestAnimationFrame(performClicks);
                        } else {
                            // Finish up
                            console.log(`✅ Auto-click sequence complete!`);
                            console.log(`📊 Final Stats:`);
                            console.log(`   - Clicks performed: ${clicksPerformed}`);
                            console.log(`   - Time elapsed: ${elapsed}ms`);
                            console.log(`   - Total clicks: ${clickCount}`);
                            console.log(`   - Blocked clicks: ${blockCount}`);
                            
                            // Show red overlay for 2 seconds
                            showBlockOverlay();
                            
                            resetAutoClickButton();
                            updateStatsTable();
                        }
                    }
                    
                    // Start the clicking sequence
                    performClicks();
                    
                } catch (error) {
                    console.error('❌ Auto-click error:', error);
                    alert('Error during auto-click. Please try again.');
                    resetAutoClickButton();
                }
            });

            function resetAutoClickButton() {
                autoClickBtn.disabled = false;
                autoClickBtn.textContent = 'Auto Click (300x)';
                isAutoClicking = false;
            }

            // Wait for iframe to load
            function waitForIframeLoad() {
                return new Promise((resolve) => {
                    if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                        resolve();
                    } else {
                        iframe.addEventListener('load', () => {
                            setTimeout(resolve, 500); // Extra delay for content
                        });
                    }
                });
            }

            // Show blocking overlay
            function showBlockOverlay() {
                overlayActive = true;
                blockOverlay.style.display = 'block';
                
                console.log('🚫 Blocking overlay activated for 2 seconds');
                
                setTimeout(() => {
                    blockOverlay.style.display = 'none';
                    overlayActive = false;
                    console.log('✅ Blocking overlay removed');
                }, 2000);
            }

            // Block overlay click handler
            blockOverlay.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🚫 Click blocked by overlay');
                
                // Show blocked click feedback
                showFeedback(e.clientX, e.clientY, '🚫', 'Click Blocked!');
                return false;
            });

            // Track when mouse enters/leaves iframe
            iframe.addEventListener('mouseenter', () => {
                mouseOverIframe = true;
                console.log('🖱️ Mouse over iframe');
            });

            iframe.addEventListener('mouseleave', () => {
                mouseOverIframe = false;
                console.log('🖱️ Mouse left iframe');
            });

            // Method 1: Detect mousedown on the container (works for first click)
            container.addEventListener('mousedown', (e) => {
                // Don't count clicks if overlay is active
                if (overlayActive) return;
                
                const rect = iframe.getBoundingClientRect();
                
                // Check if click is within iframe bounds
                if (e.clientX >= rect.left && e.clientX <= rect.right &&
                    e.clientY >= rect.top && e.clientY <= rect.bottom) {
                    
                    const now = Date.now();
                    
                    // Shorter debounce for better responsiveness
                    if (now - lastClickTime > 100) {
                        lastClickTime = now;
                        clickCount++;
                        counter.textContent = clickCount;
                        
                        showFeedback(e.clientX, e.clientY, clickCount, 'Manual Click');
                        console.log(`✅ Click #${clickCount} detected (mousedown method)`);
                        
                        updateStatsTable();
                        
                        // Remove focus from iframe briefly to reset for next click detection
                        setTimeout(() => {
                            if (document.activeElement === iframe) {
                                iframe.blur();
                                // Give it a moment then allow it to regain focus naturally
                                setTimeout(() => {
                                    // Don't force focus back, let user interaction handle it
                                }, 50);
                            }
                        }, 100);
                    }
                }
            });

            // Method 2: Enhanced focus detection with reset capability
            let iframeFocused = false;
            let lastMousePosition = { x: 0, y: 0 };
            
            // Track mouse movement over iframe
            container.addEventListener('mousemove', (e) => {
                const rect = iframe.getBoundingClientRect();
                if (e.clientX >= rect.left && e.clientX <= rect.right &&
                    e.clientY >= rect.top && e.clientY <= rect.bottom) {
                    lastMousePosition = { x: e.clientX, y: e.clientY };
                }
            });

            // Detect when iframe gains focus
            iframe.addEventListener('focus', () => {
                // Don't count clicks if overlay is active
                if (overlayActive) return;
                
                if (mouseOverIframe && !iframeFocused) {
                    const now = Date.now();
                    
                    // Only count if enough time has passed since last click
                    if (now - lastClickTime > 150) {
                        lastClickTime = now;
                        clickCount++;
                        counter.textContent = clickCount;
                        
                        showFeedback(lastMousePosition.x, lastMousePosition.y, clickCount, 'Focus Click');
                        console.log(`✅ Click #${clickCount} detected (focus method)`);
                        
                        updateStatsTable();
                        
                        // Reset focus after detection to enable next click detection
                        setTimeout(() => {
                            iframe.blur();
                            iframeFocused = false;
                        }, 150);
                    }
                }
                iframeFocused = true;
            });

            // Reset focus state when mouse leaves
            iframe.addEventListener('mouseleave', () => {
                iframeFocused = false;
            });

            // Method 3: Window blur detection with focus reset
            window.addEventListener('blur', () => {
                if (mouseOverIframe) {
                    setTimeout(() => {
                        if (document.activeElement === iframe) {
                            const now = Date.now();
                            
                            // Only count if enough time has passed
                            if (now - lastClickTime > 150) {
                                lastClickTime = now;
                                clickCount++;
                                counter.textContent = clickCount;
                                
                                const rect = iframe.getBoundingClientRect();
                                showFeedback(
                                    lastMousePosition.x || rect.left + rect.width / 2, 
                                    lastMousePosition.y || rect.top + 30, 
                                    clickCount
                                );
                                console.log(`✅ Click #${clickCount} detected (blur method)`);
                                
                                // Reset focus to enable next detection
                                setTimeout(() => {
                                    iframe.blur();
                                }, 100);
                            }
                        }
                    }, 50);
                }
            });

            function showFeedback(x, y, count, type = 'Click') {
                feedback.textContent = `${type} #${count}`;
                feedback.style.left = (x - 50) + 'px';
                feedback.style.top = (y - 30) + 'px';
                feedback.style.display = 'block';
                
                // Different colors for different types
                if (type.includes('Auto')) {
                    feedback.style.backgroundColor = 'rgba(46, 204, 113, 0.9)'; // Green
                } else if (type.includes('Blocked')) {
                    feedback.style.backgroundColor = 'rgba(231, 76, 60, 0.9)'; // Red
                } else {
                    feedback.style.backgroundColor = 'rgba(52, 152, 219, 0.9)'; // Blue
                }
                
                // Auto-hide after 1.5 seconds
                setTimeout(() => {
                    feedback.style.display = 'none';
                }, 1500);
            }

            console.log('🚀 Fixed iframe click tracker initialized - no overlay blocking!');
        })();
    </script>
</body>
</html> -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Iframe Click Protection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        #iframe-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 20px 0;
            border: 2px solid #3498db;
            border-radius: 5px;
        }
        
        #my-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        #block-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(231, 76, 60, 0.3);
            z-index: 100;
            display: none;
            cursor: not-allowed;
        }
        
        .stats-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-box {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        #clicked-box {
            background-color: rgba(46, 204, 113, 0.2);
            border: 2px solid #2ecc71;
            color: #27ae60;
        }
        
        #not-clicked-box {
            background-color: rgba(231, 76, 60, 0.2);
            border: 2px solid #e74c3c;
            color: #c0392b;
        }
        
        .stat-value {
            font-size: 2em;
            margin: 10px 0;
        }
        
        .cooldown-indicator {
            height: 5px;
            background: #3498db;
            width: 0%;
            transition: width linear;
            margin-top: 10px;
        }
        
        #click-feedback {
            position: absolute;
            background: rgba(46, 204, 113, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            display: none;
            font-weight: bold;
            z-index: 200;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h1>Iframe Click Protection Demo</h1>
    <p>Try clicking inside the Wikipedia iframe below. After first click, a 3-second cooldown will activate.</p>
    
    <div class="stats-container">
        <div class="stat-box" id="clicked-box">
            <div>Clicked</div>
            <div class="stat-value" id="clicked-count">0</div>
            <div>Valid iframe clicks</div>
        </div>
        <div class="stat-box" id="not-clicked-box">
            <div>Not Clicked</div>
            <div class="stat-value" id="not-clicked-count">0</div>
            <div>Clicks blocked by overlay</div>
        </div>
    </div>
    
    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="block-overlay"></div>
        <div id="click-feedback"></div>
    </div>
    
    <div class="cooldown-indicator" id="cooldown-indicator"></div>
    <p id="cooldown-text">Ready for first click</p>

    <script>
        (function() {
            // DOM elements
            const iframeContainer = document.getElementById('iframe-container');
            const iframe = document.getElementById('my-iframe');
            const blockOverlay = document.getElementById('block-overlay');
            const clickedCount = document.getElementById('clicked-count');
            const notClickedCount = document.getElementById('not-clicked-count');
            const cooldownIndicator = document.getElementById('cooldown-indicator');
            const cooldownText = document.getElementById('cooldown-text');
            const clickFeedback = document.getElementById('click-feedback');
            
            // State variables
            let isInCooldown = false;
            let clicked = 0;
            let notClicked = 0;
            let cooldownTimer = null;
            
            // Update the UI counters
            function updateCounters() {
                clickedCount.textContent = clicked;
                notClickedCount.textContent = notClicked;
            }
            
            // Show feedback message at click position
            function showFeedback(x, y, message, isSuccess) {
                clickFeedback.textContent = message;
                clickFeedback.style.left = (x - 50) + 'px';
                clickFeedback.style.top = (y - 30) + 'px';
                clickFeedback.style.backgroundColor = isSuccess 
                    ? 'rgba(46, 204, 113, 0.9)' 
                    : 'rgba(231, 76, 60, 0.9)';
                clickFeedback.style.display = 'block';
                
                setTimeout(() => {
                    clickFeedback.style.display = 'none';
                }, 1500);
            }
            
            // Start the cooldown period
            function startCooldown() {
                isInCooldown = true;
                blockOverlay.style.display = 'block';
                
                // Update cooldown UI
                let timeLeft = 3000;
                cooldownText.textContent = `Cooldown active - ${(timeLeft/1000).toFixed(1)}s remaining`;
                
                const startTime = Date.now();
                const endTime = startTime + 3000;
                
                function updateCooldown() {
                    const now = Date.now();
                    timeLeft = Math.max(0, endTime - now);
                    const progress = 100 - (timeLeft / 3000 * 100);
                    
                    cooldownIndicator.style.width = `${progress}%`;
                    cooldownText.textContent = `Cooldown active - ${(timeLeft/1000).toFixed(1)}s remaining`;
                    
                    if (timeLeft > 0) {
                        requestAnimationFrame(updateCooldown);
                    } else {
                        endCooldown();
                    }
                }
                
                updateCooldown();
            }
            
            // End the cooldown period
            function endCooldown() {
                isInCooldown = false;
                blockOverlay.style.display = 'none';
                cooldownText.textContent = 'Ready for next click';
                cooldownIndicator.style.width = '0%';
            }
            
            // Handle clicks on the container (which contains the iframe)
            iframeContainer.addEventListener('mousedown', function(e) {
                // Check if click is within iframe bounds
                const rect = iframe.getBoundingClientRect();
                const isInIframe = e.clientX >= rect.left && e.clientX <= rect.right &&
                                  e.clientY >= rect.top && e.clientY <= rect.bottom;
                
                if (!isInIframe) return;
                
                if (isInCooldown) {
                    // Click is blocked by overlay
                    notClicked++;
                    updateCounters();
                    showFeedback(e.clientX, e.clientY, 'Click Blocked!', false);
                    console.log('Blocked click at:', e.clientX, e.clientY);
                } else {
                    // First valid click
                    clicked++;
                    updateCounters();
                    showFeedback(e.clientX, e.clientY, 'Click Registered!', true);
                    console.log('Valid click at:', e.clientX, e.clientY);
                    
                    // Start cooldown
                    startCooldown();
                }
            });
            
            // Initialize
            updateCounters();
            console.log('Click protection system initialized');
        })();
    </script>
</body>
</html>