<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Iframe Click Protection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        #iframe-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 20px 0;
            border: 2px solid #3498db;
            border-radius: 5px;
        }

        #my-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        #block-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(231, 76, 60, 0.3);
            z-index: 100;
            display: none;
            cursor: not-allowed;
        }

        .stats-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-box {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        #clicked-box {
            background-color: rgba(46, 204, 113, 0.2);
            border: 2px solid #2ecc71;
            color: #27ae60;
        }

        #not-clicked-box {
            background-color: rgba(231, 76, 60, 0.2);
            border: 2px solid #e74c3c;
            color: #c0392b;
        }

        .stat-value {
            font-size: 2em;
            margin: 10px 0;
        }

        .cooldown-indicator {
            height: 5px;
            background: #3498db;
            width: 0%;
            transition: width linear;
            margin-top: 10px;
        }

        #click-feedback {
            position: absolute;
            background: rgba(46, 204, 113, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            display: none;
            font-weight: bold;
            z-index: 200;
            pointer-events: none;
        }

        .test-controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        #test-button {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            margin-right: 10px;
        }

        #test-button:hover {
            background: #c0392b;
        }

        #test-button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }

        #test-status {
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Iframe Click Protection Demo</h1>
    <p>Try clicking inside the Wikipedia iframe below. After first click, a 3-second cooldown will activate.</p>

    <div class="stats-container">
        <div class="stat-box" id="clicked-box">
            <div>Clicked</div>
            <div class="stat-value" id="clicked-count">0</div>
            <div>Valid iframe clicks</div>
        </div>
        <div class="stat-box" id="not-clicked-box">
            <div>Not Clicked</div>
            <div class="stat-value" id="not-clicked-count">0</div>
            <div>Clicks blocked by overlay</div>
        </div>
    </div>

    <div class="test-controls">
        <button id="test-button">Run Puppeteer Test (300 clicks)</button>
        <div id="test-status">Ready to test</div>
    </div>

    <div id="iframe-container">
        <iframe id="my-iframe" src="https://en.wikipedia.org/wiki/Cricket"></iframe>
        <div id="block-overlay"></div>
        <div id="click-feedback"></div>
    </div>

    <div class="cooldown-indicator" id="cooldown-indicator"></div>
    <p id="cooldown-text">Ready for first click</p>

    <script>
        (function() {
            // DOM elements
            const iframeContainer = document.getElementById('iframe-container');
            const iframe = document.getElementById('my-iframe');
            const blockOverlay = document.getElementById('block-overlay');
            const clickedCount = document.getElementById('clicked-count');
            const notClickedCount = document.getElementById('not-clicked-count');
            const cooldownIndicator = document.getElementById('cooldown-indicator');
            const cooldownText = document.getElementById('cooldown-text');
            const clickFeedback = document.getElementById('click-feedback');
            const testButton = document.getElementById('test-button');
            const testStatus = document.getElementById('test-status');

            // State variables
            let isInCooldown = false;
            let clicked = 0;
            let notClicked = 0;
            let cooldownTimer = null;
            let capturedClickPosition = null; // Store the position of the first click
            let lastClickTime = 0; // Track last click time for debouncing

            // Update the UI counters
            function updateCounters() {
                clickedCount.textContent = clicked;
                notClickedCount.textContent = notClicked;
            }

            // Show feedback message at click position
            function showFeedback(x, y, message, isSuccess) {
                clickFeedback.textContent = message;
                clickFeedback.style.left = (x - 50) + 'px';
                clickFeedback.style.top = (y - 30) + 'px';
                clickFeedback.style.backgroundColor = isSuccess
                    ? 'rgba(46, 204, 113, 0.9)'
                    : 'rgba(231, 76, 60, 0.9)';
                clickFeedback.style.display = 'block';

                setTimeout(() => {
                    clickFeedback.style.display = 'none';
                }, 1500);
            }

            // Start the cooldown period
            function startCooldown() {
                isInCooldown = true;
                blockOverlay.style.display = 'block';

                // Update cooldown UI
                let timeLeft = 3000;
                cooldownText.textContent = `Cooldown active - ${(timeLeft/1000).toFixed(1)}s remaining`;

                const startTime = Date.now();
                const endTime = startTime + 3000;

                function updateCooldown() {
                    const now = Date.now();
                    timeLeft = Math.max(0, endTime - now);
                    const progress = 100 - (timeLeft / 3000 * 100);

                    cooldownIndicator.style.width = `${progress}%`;
                    cooldownText.textContent = `Cooldown active - ${(timeLeft/1000).toFixed(1)}s remaining`;

                    if (timeLeft > 0) {
                        requestAnimationFrame(updateCooldown);
                    } else {
                        endCooldown();
                    }
                }

                updateCooldown();
            }

            // End the cooldown period
            function endCooldown() {
                isInCooldown = false;
                blockOverlay.style.display = 'none';
                cooldownText.textContent = 'Ready for next click';
                cooldownIndicator.style.width = '0%';

                console.log('✅ Cooldown ended, iframe ready for next click');

                // If we have a captured click position, fire the click at that position
                if (capturedClickPosition) {
                    console.log('🎯 Firing captured click at:', capturedClickPosition);

                    // Temporarily disable our click detection to prevent counting the synthetic click
                    const tempDisable = Date.now();
                    lastClickTime = tempDisable;

                    setTimeout(() => {
                        try {
                            if (iframe.contentDocument) {
                                const iframeDoc = iframe.contentDocument;
                                const rect = iframe.getBoundingClientRect();
                                const relativeX = capturedClickPosition.x - rect.left;
                                const relativeY = capturedClickPosition.y - rect.top;

                                const elementAtPoint = iframeDoc.elementFromPoint(relativeX, relativeY);
                                if (elementAtPoint) {
                                    elementAtPoint.click();
                                    console.log('🎯 Synthetic click fired on:', elementAtPoint.tagName);
                                } else {
                                    console.log('🎯 No element found at captured position');
                                }
                            }
                        } catch (e) {
                            console.log('⚠️ Could not fire synthetic click:', e.message);
                        }
                    }, 100);

                    // Clear the captured position
                    capturedClickPosition = null;
                }
            }

            // Handle clicks on the overlay (blocked clicks)
            blockOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('🚫 Overlay click detected at:', e.clientX, e.clientY);

                // Capture the click position for later use
                capturedClickPosition = { x: e.clientX, y: e.clientY };

                // Count as blocked click
                notClicked++;
                updateCounters();
                showFeedback(e.clientX, e.clientY, 'Click Blocked!', false);
                console.log('🚫 Blocked click count increased to:', notClicked);

                return false;
            });

            // Also handle mousedown on overlay for better detection
            blockOverlay.addEventListener('mousedown', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('🚫 Overlay mousedown detected at:', e.clientX, e.clientY);

                // Capture the click position for later use
                capturedClickPosition = { x: e.clientX, y: e.clientY };

                // Count as blocked click
                notClicked++;
                updateCounters();
                showFeedback(e.clientX, e.clientY, 'Click Blocked!', false);
                console.log('🚫 Blocked click count increased to:', notClicked);

                return false;
            });

            // Handle clicks on the iframe container (for Puppeteer clicks)
            iframeContainer.addEventListener('click', function(e) {
                // Check if click is within iframe bounds
                const rect = iframe.getBoundingClientRect();
                const isInIframe = e.clientX >= rect.left && e.clientX <= rect.right &&
                                  e.clientY >= rect.top && e.clientY <= rect.bottom;

                if (!isInIframe) return;

                // If overlay is active, this click should be handled by the overlay
                if (isInCooldown) return;

                // This is a valid click on the iframe (mainly for Puppeteer)
                clicked++;
                updateCounters();
                showFeedback(e.clientX, e.clientY, 'Click Registered!', true);
                console.log('Valid click at:', e.clientX, e.clientY);

                // Start cooldown
                startCooldown();
            });

            // Detect real user clicks inside the iframe content
            let mouseOverIframe = false;

            // Track when mouse enters/leaves iframe
            iframe.addEventListener('mouseenter', () => {
                mouseOverIframe = true;
                console.log('🖱️ Mouse over iframe');
            });

            iframe.addEventListener('mouseleave', () => {
                mouseOverIframe = false;
                console.log('🖱️ Mouse left iframe');
            });

            // Simple and reliable click detection
            let iframeFocused = false;

            // Method 1: Direct mousedown detection on iframe container
            iframeContainer.addEventListener('mousedown', function(e) {
                console.log('🖱️ Mousedown detected at:', e.clientX, e.clientY);

                const rect = iframe.getBoundingClientRect();
                const isInIframe = e.clientX >= rect.left && e.clientX <= rect.right &&
                                  e.clientY >= rect.top && e.clientY <= rect.bottom;

                console.log('📍 Is in iframe bounds:', isInIframe, 'Cooldown active:', isInCooldown);

                if (isInIframe) {
                    if (isInCooldown) {
                        // This should be handled by overlay, but just in case
                        console.log('🚫 Click during cooldown - should be blocked');
                        return;
                    }

                    const now = Date.now();
                    if (now - lastClickTime > 200) { // Debounce
                        lastClickTime = now;
                        clicked++;
                        updateCounters();
                        showFeedback(e.clientX, e.clientY, 'Click Registered!', true);
                        console.log('✅ Manual click registered! Count:', clicked);

                        // Start cooldown
                        startCooldown();
                    }
                }
            });

            // Method 2: Iframe focus detection (backup for clicks inside iframe content)
            iframe.addEventListener('focus', () => {
                console.log('🎯 Iframe focused, cooldown:', isInCooldown, 'mouse over:', mouseOverIframe);

                if (isInCooldown || !mouseOverIframe) return;

                if (!iframeFocused) {
                    const now = Date.now();
                    if (now - lastClickTime > 200) { // Debounce
                        lastClickTime = now;
                        clicked++;
                        updateCounters();

                        const rect = iframe.getBoundingClientRect();
                        showFeedback(rect.left + rect.width/2, rect.top + 30, 'Click Registered!', true);
                        console.log('✅ Manual click via focus! Count:', clicked);

                        // Start cooldown
                        startCooldown();
                    }
                }
                iframeFocused = true;
            });

            // Method 3: Window blur detection (when iframe steals focus)
            window.addEventListener('blur', () => {
                if (isInCooldown || !mouseOverIframe) return;

                setTimeout(() => {
                    if (document.activeElement === iframe && !iframeFocused) {
                        const now = Date.now();
                        if (now - lastClickTime > 200) {
                            lastClickTime = now;
                            clicked++;
                            updateCounters();

                            const rect = iframe.getBoundingClientRect();
                            showFeedback(rect.left + rect.width/2, rect.top + 30, 'Click Registered!', true);
                            console.log('✅ Manual click via window blur! Count:', clicked);

                            // Start cooldown
                            startCooldown();
                        }
                        iframeFocused = true;
                    }
                }, 50);
            });

            // Reset iframe focus state
            iframe.addEventListener('blur', () => {
                iframeFocused = false;
                console.log('📤 Iframe lost focus');
            });

            // Reset focus state when mouse leaves iframe
            iframe.addEventListener('mouseleave', () => {
                iframeFocused = false;
                console.log('🖱️ Mouse left iframe');
            });

            // Test button functionality
            testButton.addEventListener('click', async function() {
                testButton.disabled = true;
                testStatus.textContent = 'Running Puppeteer test...';

                try {
                    const response = await fetch('/api/run-test', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (result.success) {
                        testStatus.textContent = `Test completed! Duration: ${result.duration}ms, Clicks/sec: ${result.clicksPerSecond}`;
                    } else {
                        testStatus.textContent = `Test failed: ${result.error}`;
                    }
                } catch (error) {
                    testStatus.textContent = `Test error: ${error.message}`;
                }

                testButton.disabled = false;
            });

            // Initialize
            updateCounters();
            console.log('Click protection system initialized');
        })();
    </script>
</body>
</html>