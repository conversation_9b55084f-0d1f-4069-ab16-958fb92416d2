
const express = require('express');
const puppeteer = require('puppeteer');
const app = express();
const PORT = 4000;

app.use(express.static('public'));
app.use(express.json());

let browser = null;

async function initBrowser() {
    try {
        browser = await puppeteer.launch({
            headless: false,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        console.log('✅ Browser ready');
    } catch (error) {
        console.error('❌ Browser init failed:', error);
    }
}

app.post('/api/run-test', async (req, res) => {
    console.log('[API] Starting automated click test - 300 clicks in 2 seconds');
    if (!browser) await initBrowser();

    try {
        const page = await browser.newPage();

        // Enable console logging from the page
        page.on('console', msg => {
            console.log(`[BROWSER] ${msg.text()}`);
        });

        await page.goto(`http://localhost:${PORT}`, { waitUntil: 'networkidle2' });

        // Wait for the iframe container to be ready
        await page.waitForSelector('#iframe-container', { timeout: 10000 });

        // Get iframe container position and dimensions (parent element of iframe)
        const containerBounds = await page.evaluate(() => {
            const container = document.getElementById('iframe-container');
            const rect = container.getBoundingClientRect();
            return {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height
            };
        });

        console.log('📦 Container bounds:', containerBounds);

        const clickCount = 300;
        const targetDuration = 2000; // 2 seconds
        const startTime = Date.now();

        // Wait for page to fully load
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log(`🎯 Starting ${clickCount} clicks in ${targetDuration}ms`);

        // Perform rapid clicks
        for (let i = 0; i < clickCount; i++) {
            // Generate random coordinates within container bounds
            const x = containerBounds.x + Math.random() * containerBounds.width;
            const y = containerBounds.y + Math.random() * containerBounds.height;

            console.log(`Click ${i + 1}/300 at (${x.toFixed(1)}, ${y.toFixed(1)})`);

            // Use page.mouse.click for more realistic clicking
            await page.mouse.click(x, y);

            // Very small delay to achieve ~150 clicks per second (300 clicks in 2 seconds)
            await new Promise(resolve => setTimeout(resolve, 6));
        }

        const duration = Date.now() - startTime;
        console.log(`✅ Test completed in ${duration}ms`);

        // Wait a moment for UI to update
        await new Promise(resolve => setTimeout(resolve, 500));

        // Get final stats from the page
        const stats = await page.evaluate(() => {
            return {
                clicked: parseInt(document.getElementById('clicked-count').textContent),
                notClicked: parseInt(document.getElementById('not-clicked-count').textContent)
            };
        });

        console.log('📊 Final stats:', stats);

        await page.close();

        res.json({
            success: true,
            duration: duration,
            clicksPerSecond: (clickCount / (duration/1000)).toFixed(1),
            stats: stats,
            expected: {
                clicked: 1,
                notClicked: 299
            },
            message: `Expected: 1 valid click, 299 blocked. Actual: ${stats.clicked} valid, ${stats.notClicked} blocked`
        });

    } catch (error) {
        console.error('❌ Test failed:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

app.listen(PORT, async () => {
    console.log(`Server running on http://localhost:${PORT}`);
    await initBrowser();
});