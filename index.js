// const express = require('express');
// const puppeteer = require('puppeteer');
// const app = express();
// const PORT = 4000;

// app.use(express.static('public'));
// app.use(express.json());

// let browser = null;
// let clickProtection = {
//     isBlocked: false,
//     lastClickTime: 0
// };
// let clickStats = {
//     allowed: 0,
//     blocked: 0,
//     total: 0
// };

// async function initBrowser() {
//     try {
//         browser = await puppeteer.launch({
//             headless: false, // Visible for debugging
//             args: ['--no-sandbox', '--disable-setuid-sandbox']
//         });
//         console.log('✅ Browser ready');
//     } catch (error) {
//         console.error('❌ Browser init failed:', error);
//     }
// }

// app.get('/api/stats', (req, res) => {
//     console.log(`[API] Stats requested - Allowed: ${clickStats.allowed}, Blocked: ${clickStats.blocked}, Total: ${clickStats.total}`);
//     res.json(clickStats);
// });

// app.post('/api/click', (req, res) => {
//     const { x, y } = req.body;
//     const now = Date.now();

//     clickStats.total++;

//     // Check if this is the first click (no protection active)
//     if (!clickProtection.isBlocked) {
//         // First click - allow it and activate protection
//         clickStats.allowed++;
//         clickProtection.isBlocked = true;
//         clickProtection.lastClickTime = now;

//         console.log(`[API] ✅ FIRST CLICK ALLOWED at (${x}, ${y}) - Protection activated`);

//         // Schedule protection removal after 3 seconds
//         setTimeout(() => {
//             clickProtection.isBlocked = false;
//             console.log(`[API] 🔓 Protection removed - Ready for next click`);
//         }, 3000);

//         res.json({
//             success: true,
//             message: 'First click allowed - Protection activated',
//             isFirstClick: true,
//             addOverlay: true
//         });
//     } else {
//         // Subsequent click during protection - block it
//         clickStats.blocked++;
//         const remainingTime = Math.max(0, 3000 - (now - clickProtection.lastClickTime));

//         console.log(`[API] 🚫 SUBSEQUENT CLICK BLOCKED at (${x}, ${y}) - ${remainingTime}ms remaining`);

//         res.json({
//             success: false,
//             blocked: true,
//             message: 'Subsequent click blocked during cooldown',
//             isFirstClick: false,
//             remainingTime: remainingTime
//         });
//     }
// });

// app.post('/api/run-test', async (req, res) => {
//     console.log('[API] Starting automated click test');
//     if (!browser) await initBrowser();

//     try {
//         const page = await browser.newPage();
        
//         // Enable console logging from the page
//         page.on('console', msg => {
//             console.log(`[BROWSER] ${msg.text()}`);
//         });
        
//         await page.goto(`http://localhost:${PORT}`, { waitUntil: 'networkidle2' });

//         // Wait for the iframe container to be ready
//         await page.waitForSelector('#iframe-container', { timeout: 10000 });
        
//         // Get iframe container position and dimensions
//         const containerBounds = await page.evaluate(() => {
//             const container = document.getElementById('iframe-container');
//             const rect = container.getBoundingClientRect();
//             return {
//                 x: rect.left,
//                 y: rect.top,
//                 width: rect.width,
//                 height: rect.height
//             };
//         });
        
//         console.log('📦 Container bounds:', containerBounds);
        
//         // Calculate center coordinates within the iframe container
//         const clickX = containerBounds.x + (containerBounds.width / 2);
//         const clickY = containerBounds.y + (containerBounds.height / 2);
        
//         console.log(`🎯 Will click at (${clickX}, ${clickY}) 300 times`);
        
//         const clickCount = 300;
//         const startTime = Date.now();
        
//         // Wait for page to fully load
//         await new Promise(resolve => setTimeout(resolve, 2000));
        
//         // More reliable approach: directly trigger the click handlers
//         for (let i = 0; i < clickCount; i++) {
//             await page.evaluate((x, y) => {
//                 console.log(`[TEST] Triggering click at (${x}, ${y})`);
                
//                 const container = document.getElementById('iframe-container');
//                 if (!container) {
//                     console.log('[TEST] Container not found!');
//                     return false;
//                 }
                
//                 const rect = container.getBoundingClientRect();
//                 const relativeX = x - rect.left;
//                 const relativeY = y - rect.top;
                
//                 // Create a proper MouseEvent
//                 const event = new MouseEvent('click', {
//                     view: window,
//                     bubbles: true,
//                     cancelable: true,
//                     clientX: x,
//                     clientY: y,
//                     screenX: x,
//                     screenY: y,
//                     pointerId: 1,
//                     isPrimary: true
//                 });
                
//                 // Set the target as the container
//                 Object.defineProperty(event, 'target', {
//                     value: container,
//                     enumerable: true
//                 });
                
//                 // Dispatch the event
//                 const result = container.dispatchEvent(event);
//                 console.log(`[TEST] Click dispatched, result: ${result}, coords: (${relativeX}, ${relativeY})`);
                
//                 return { success: true, relativeX, relativeY };
//             }, clickX, clickY);
            
//             // Small delay between clicks
//             await new Promise(resolve => setTimeout(resolve, 5));
//         }
        
//         const duration = Date.now() - startTime;
//         console.log(`✅ Test completed in ${duration}ms`);

//         await page.close();
//         res.json({ 
//             success: true,
//             duration: duration,
//             clicksPerSecond: (clickCount / (duration/1000)).toFixed(1),
//             coordinates: { x: clickX, y: clickY },
//             containerBounds: containerBounds
//         });
        
//     } catch (error) {
//         console.error('❌ Test failed:', error);
//         res.status(500).json({ success: false, error: error.message });
//     }
// });

// // Reset stats endpoint for testing
// app.post('/api/reset', (req, res) => {
//     clickStats = { allowed: 0, blocked: 0, total: 0 };
//     clickProtection = { isBlocked: false, lastClickTime: 0 };
//     console.log('[API] Stats reset');
//     res.json({ success: true, message: 'Stats reset' });
// });

// app.listen(PORT, async () => {
//     console.log(`Server running on http://localhost:${PORT}`);
//     await initBrowser();
// });

const express = require('express');
const puppeteer = require('puppeteer');
const app = express();
const PORT = 4000;

app.use(express.static('public'));
app.use(express.json());

let browser = null;

async function initBrowser() {
    try {
        browser = await puppeteer.launch({
            headless: false,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        console.log('✅ Browser ready');
    } catch (error) {
        console.error('❌ Browser init failed:', error);
    }
}

app.post('/api/run-test', async (req, res) => {
    console.log('[API] Starting automated click test');
    if (!browser) await initBrowser();

    try {
        const page = await browser.newPage();
        
        // Enable console logging from the page
        page.on('console', msg => {
            console.log(`[BROWSER] ${msg.text()}`);
        });
        
        await page.goto(`http://localhost:${PORT}`, { waitUntil: 'networkidle2' });

        // Wait for the iframe container to be ready
        await page.waitForSelector('#iframe-container', { timeout: 10000 });
        
        // Get iframe container position and dimensions
        const containerBounds = await page.evaluate(() => {
            const container = document.getElementById('iframe-container');
            const rect = container.getBoundingClientRect();
            return {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height
            };
        });
        
        console.log('📦 Container bounds:', containerBounds);
        
        const clickCount = 300;
        const startTime = Date.now();
        
        // Wait for page to fully load
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Perform clicks
        for (let i = 0; i < clickCount; i++) {
            // Generate random coordinates within container bounds
            const x = containerBounds.x + Math.random() * containerBounds.width;
            const y = containerBounds.y + Math.random() * containerBounds.height;
            
            await page.mouse.click(x, y);
            
            // Small delay between clicks
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        const duration = Date.now() - startTime;
        console.log(`✅ Test completed in ${duration}ms`);

        // Get final stats from the page
        const stats = await page.evaluate(() => {
            return {
                clicked: parseInt(document.getElementById('clicked-count').textContent),
                notClicked: parseInt(document.getElementById('not-clicked-count').textContent)
            };
        });
        
        await page.close();
        
        res.json({ 
            success: true,
            duration: duration,
            clicksPerSecond: (clickCount / (duration/1000)).toFixed(1),
            stats: stats,
            expected: {
                clicked: 1,
                notClicked: 299
            }
        });
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

app.listen(PORT, async () => {
    console.log(`Server running on http://localhost:${PORT}`);
    await initBrowser();
});